#!/bin/bash
# 【脚本用途】
# 动态使用 stress-ng 工具将系统的 CPU 和内存使用率维持在指定区间内
#
#
# 【适用系统】
# Ubuntu / CentOS / RHEL 等主流 Linux 发行版
#
# 【依赖工具】
# stress-ng (脚本会自动检测并安装)
#
# 【使用方法】
# ./a.sh           # 静默模式运行 (不输出调试信息)
# ./a.sh --debug   # 调试模式运行 (输出详细信息)
# =============================================================================

# ---------------- 参数解析 ----------------
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --debug)
                DEBUG=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --debug    启用调试模式，输出详细信息"
                echo "  -h, --help 显示此帮助信息"
                exit 0
                ;;
            *)
                echo "未知选项: $1"
                echo "使用 $0 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# ---------------- 调试输出函数 ----------------
debug_echo() {
    if [ "$DEBUG" = true ]; then
        echo "$@"
    fi
}

# ---------------- 配置参数 ----------------
INTERVAL=10    # 检测间隔 (秒)
LOW=40         # 最低使用率阈值 (%)
HIGH=60        # 最高使用率阈值 (%)
TARGET=50      # 目标使用率 (%)
TIMEOUT=3600   # 压测命令超时时间 (秒，默认1小时)

# 运行时变量
CPU_WORKERS=0  # 当前 CPU 压力测试进程数
MEM_GB=0       # 当前内存压力测试分配量 (GB)
CPU_PID=0      # CPU 压力测试进程 PID
MEM_PID=0      # 内存压力测试进程 PID
DEBUG=false    # 调试模式开关

# ---------------- 检查并安装 stress-ng ----------------
# 支持: Ubuntu/Debian (apt) 和 CentOS/RHEL (yum) 系统
install_stressng() {
    if ! command -v stress-ng &>/dev/null; then
        debug_echo "[INFO] stress-ng 未安装，尝试安装..."
        if [ -f /etc/debian_version ]; then
            # Debian/Ubuntu 系统使用 apt 包管理器
            if [ "$DEBUG" = true ]; then
                sudo apt-get update && sudo apt-get install -y stress-ng
            else
                sudo apt-get update >/dev/null 2>&1 && sudo apt-get install -y stress-ng >/dev/null 2>&1
            fi
        elif [ -f /etc/redhat-release ]; then
            # CentOS/RHEL 系统使用 yum 包管理器
            if [ "$DEBUG" = true ]; then
                sudo yum install -y epel-release && sudo yum install -y stress-ng
            else
                sudo yum install -y epel-release >/dev/null 2>&1 && sudo yum install -y stress-ng >/dev/null 2>&1
            fi
        else
            echo "[ERROR] 不支持的系统，请手动安装 stress-ng" >&2
            exit 1
        fi
    fi
}

# ---------------- 获取系统信息 ----------------
get_sysinfo() {
    CPU_CORES=$(nproc)                                    # 获取 CPU 核心数
    MEM_TOTAL=$(free -m | awk '/Mem:/ {print $2}')       # 获取内存总量 (MB)
    debug_echo "[INFO] CPU核心数: $CPU_CORES | 内存总量: ${MEM_TOTAL}MB"
}

# ---------------- 获取实时使用率 ----------------
# 功能: 实时获取系统的 CPU 和内存使用率
# 返回: 整数百分比 (0-100)
get_cpu_usage() {
    local idle
    idle=$(top -bn1 | grep "Cpu(s)" | awk '{print $8}' | cut -d. -f1)
    echo $((100 - idle))
}

# 获取内存使用率
get_mem_usage() {
    free | awk '/Mem:/ {printf "%.0f", $3/$2 * 100}'
}

# ---------------- 启动/重启 stress-ng 进程 ----------------
# 功能: 管理 CPU 和内存压力测试进程的启动、重启和停止

# 启动/重启 CPU 压力测试
start_cpu_stress() {
    # 如果有旧的 CPU 压力测试进程，先终止它
    if [ $CPU_PID -ne 0 ]; then
        kill -9 $CPU_PID >/dev/null 2>&1
        wait $CPU_PID 2>/dev/null  # 等待进程完全结束，避免显示 Killed 消息
    fi

    # 根据需要的核心数启动新的压力测试
    if [ $CPU_WORKERS -gt 0 ]; then
        # 启动 CPU 压力测试，运行 1 小时后自动结束
        stress-ng --cpu "$CPU_WORKERS" -t $TIMEOUT >/dev/null 2>&1 &
        CPU_PID=$!
        debug_echo "[CPU] stress-ng --cpu $CPU_WORKERS (PID=$CPU_PID)"
    else
        CPU_PID=0  # 不需要压力测试时，清零 PID
    fi
}

# 启动/重启内存压力测试
start_mem_stress() {
    # 如果有旧的内存压力测试进程，先终止它
    if [ $MEM_PID -ne 0 ]; then
        kill -9 $MEM_PID >/dev/null 2>&1
        wait $MEM_PID 2>/dev/null  # 等待进程完全结束，避免显示 Killed 消息
    fi

    # 根据需要的内存量启动新的压力测试
    if [ $MEM_GB -gt 0 ]; then
        local mem_bytes=$((MEM_GB * 1024))M  # 转换为 MB 单位
        # 启动内存压力测试：10个虚拟机进程，每个分配指定内存，保持60秒，总运行1小时
        stress-ng --vm 10 --vm-bytes "$mem_bytes" --vm-hang 60 -t $TIMEOUT >/dev/null 2>&1 &
        MEM_PID=$!
        debug_echo "[MEM] stress-ng --vm 10 --vm-bytes $mem_bytes (PID=$MEM_PID)"
    else
        MEM_PID=0  # 不需要压力测试时，清零 PID
    fi
}

# ============================================================================
# 主控制循环 - 智能负载调整算法
# ============================================================================
# 功能: 持续监控系统负载，根据使用率智能调整 CPU 和内存压力
# 策略:
#   - 在目标区间 (40%-60%) 内: 保持稳定，不做调整
#   - 超出区间: 使用智能算法快速收敛到目标值 (50%)

main_loop() {
    while true; do
        # 获取当前系统使用率
        cpu=$(get_cpu_usage)
        mem=$(get_mem_usage)
        debug_echo "[INFO] CPU=${cpu}% | MEM=${mem}% | $(date)"

        # ========== CPU 负载调整 (智能比例算法) ==========
        if [ "$cpu" -ge "$LOW" ] && [ "$cpu" -le "$HIGH" ]; then
            # CPU 使用率在目标区间内，保持稳定
            debug_echo "[CPU] CPU 在区间 (${cpu}%)，无需调整"
        else
            # CPU 使用率超出区间，需要调整
            diff=$((TARGET - cpu))                    # 计算误差

            # 智能计算目标核心数
            if [ $cpu -gt 0 ] && [ $CPU_WORKERS -gt 0 ]; then
                # 基于当前使用率和核心数，计算达到目标使用率所需的核心数
                # 公式: 目标核心数 = 当前核心数 * 目标使用率 / 当前使用率
                target_workers=$(( (CPU_WORKERS * TARGET + cpu / 2) / cpu ))

                # 限制目标核心数在合理范围内
                if [ $target_workers -gt $CPU_CORES ]; then
                    target_workers=$CPU_CORES
                elif [ $target_workers -lt 0 ]; then
                    target_workers=0
                fi

                # 平滑调整：不要一次性跳跃太大
                if [ $target_workers -gt $CPU_WORKERS ]; then
                    # 需要增加核心数，最多一次增加当前的50%，但不超过物理核心数
                    max_increase=$(( (CPU_WORKERS + 1) / 2 ))
                    if [ $max_increase -lt 1 ]; then max_increase=1; fi
                    adjustment=$(( target_workers - CPU_WORKERS ))
                    if [ $adjustment -gt $max_increase ]; then
                        adjustment=$max_increase
                    fi
                    # 确保不超过物理核心数
                    if [ $((CPU_WORKERS + adjustment)) -gt $CPU_CORES ]; then
                        adjustment=$((CPU_CORES - CPU_WORKERS))
                    fi
                else
                    # 需要减少核心数，最多一次减少当前的50%
                    max_decrease=$(( (CPU_WORKERS + 1) / 2 ))
                    if [ $max_decrease -lt 1 ]; then max_decrease=1; fi
                    adjustment=$(( target_workers - CPU_WORKERS ))
                    if [ $adjustment -lt -$max_decrease ]; then
                        adjustment=-$max_decrease
                    fi
                fi
            else
                # 初始状态或特殊情况，使用智能的比例调整
                # 根据CPU核心数动态调整比例因子
                if [ $CPU_CORES -le 4 ]; then
                    # 4核心及以下，使用较小的调整幅度
                    adjustment=$((diff / 8))
                elif [ $CPU_CORES -le 8 ]; then
                    # 5-8核心，使用中等调整幅度
                    adjustment=$((diff / 6))
                else
                    # 8核心以上，使用较大调整幅度
                    adjustment=$((diff / 5))
                fi

                # 确保最小调整量，但不超过物理核心数
                if [ $adjustment -eq 0 ] && [ $diff -gt 0 ]; then
                    adjustment=1
                elif [ $adjustment -eq 0 ] && [ $diff -lt 0 ]; then
                    adjustment=-1
                fi

                # 限制调整幅度不超过物理核心数
                if [ $adjustment -gt 0 ] && [ $((CPU_WORKERS + adjustment)) -gt $CPU_CORES ]; then
                    adjustment=$((CPU_CORES - CPU_WORKERS))
                fi
            fi

            # 计算新的核心数并限制在合理范围内
            new_workers=$((CPU_WORKERS + adjustment))
            if [ $new_workers -lt 0 ]; then
                new_workers=0                         # 最少0核心
            elif [ $new_workers -gt $CPU_CORES ]; then
                new_workers=$CPU_CORES                # 最多不超过物理核心数
            fi

            # 应用调整
            if [ $new_workers -ne $CPU_WORKERS ]; then
                CPU_WORKERS=$new_workers
                start_cpu_stress
                debug_echo "[CPU] 超出区间 (${cpu}%), 差值=${diff}%, 调整=${adjustment}, 新核心数=${CPU_WORKERS}"
            else
                debug_echo "[CPU] CPU 已达到边界，无需调整"
            fi
        fi

        # ========== 内存负载调整 (动态分配算法) ==========
        # 算法原理: 直接计算目标内存需求，一步到位
        # 优势: 避免多次小幅调整，快速达到目标值
        if [ "$mem" -ge "$LOW" ] && [ "$mem" -le "$HIGH" ]; then
            # 内存使用率在目标区间内，保持稳定
            debug_echo "[MEM] 内存在区间 (${mem}%)，无需调整"
        else
            # 内存使用率超出区间，需要调整
            mem_diff=$((TARGET - mem))                           # 计算误差

            # 计算目标内存分配量
            target_mem_mb=$((MEM_TOTAL * TARGET / 100))          # 目标内存使用量 (MB)
            current_used_mb=$((MEM_TOTAL * mem / 100))           # 当前内存使用量 (MB)
            need_mb=$((target_mem_mb - current_used_mb))         # 需要调整的内存量 (MB)

            # 转换为 GB 单位 (向上取整以确保足够的内存分配)
            if [ $need_mb -gt 0 ]; then
                need_gb=$(((need_mb + 1023) / 1024))             # 正数向上取整
            else
                need_gb=$((need_mb / 1024))                      # 负数直接除法
            fi

            # 计算新的内存分配量
            new_mem_gb=$((MEM_GB + need_gb))

            # 安全限制: 最多使用 80% 物理内存，避免系统不稳定
            max_mem_gb=$((MEM_TOTAL * 80 / 100 / 1024))
            if [ $new_mem_gb -lt 0 ]; then
                new_mem_gb=0                                     # 最少0GB
            elif [ $new_mem_gb -gt $max_mem_gb ]; then
                new_mem_gb=$max_mem_gb                           # 最多不超过80%物理内存
            fi

            # 应用调整
            if [ $new_mem_gb -ne $MEM_GB ]; then
                MEM_GB=$new_mem_gb
                start_mem_stress
                debug_echo "[MEM] 超出区间 (${mem}%), 差值=${mem_diff}%, 需要=${need_mb}MB, 新分配=${MEM_GB}GB"
            else
                debug_echo "[MEM] 内存已达到边界，无需调整"
            fi
        fi

        # 等待下一次检测
        sleep $INTERVAL
    done
}

# ============================================================================
# 脚本执行入口
# ============================================================================
# 执行流程:
# 1. 解析命令行参数
# 2. 检查并安装 stress-ng 工具
# 3. 获取系统基础信息 (CPU核心数、内存总量)
# 4. 启动主控制循环，开始智能负载管理

parse_arguments "$@"  # 解析命令行参数

if [ "$DEBUG" = true ]; then
    echo "============================================================================"
    echo "智能系统负载控制脚本启动 (调试模式)"
    echo "目标区间: ${LOW}% - ${HIGH}%"
    echo "目标值: ${TARGET}%"
    echo "检测间隔: ${INTERVAL}秒"
    echo "压测超时: ${TIMEOUT}秒 ($(($TIMEOUT/3600))小时)"
    echo "============================================================================"
fi

install_stressng    # 安装依赖工具
get_sysinfo        # 获取系统信息
main_loop          # 开始主控制循环
